#if defined(_WIN32)
#define NOMINMAX
#endif
#include "audio_engine.h"
#include "piano_keyboard.h"
#include <iostream>
#include <vector>
#include <cstring>
#include <thread>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <dlfcn.h>
#endif

// Note: All BASS constants and structures are now defined in the official headers
// MAKEWORD is also defined in bass.h

// Helper function to join path components
std::string JoinPath(const std::string& p1, const std::string& p2) {
#ifdef _WIN32
    if (p1.empty()) return p2;
    if (p2.empty()) return p1;
    char last_char = p1.back();
    if (last_char == '\\' || last_char == '/') {
        return p1 + p2;
    }
    return p1 + "\\" + p2;
#else
    if (p1.empty()) return p2;
    if (p2.empty()) return p1;
    char last_char = p1.back();
    if (last_char == '/') {
        return p1 + p2;
    }
    return p1 + "/" + p2;
#endif
}

// Helper function to get executable directory
#ifdef _WIN32
std::string GetExecutableDirectory() {
    char path[MAX_PATH];
    if (GetModuleFileNameA(NULL, path, MAX_PATH) == 0) {
        return ".";
    }
    std::string exe_path(path);
    size_t last_slash = exe_path.find_last_of('\\');
    if (last_slash != std::string::npos) {
        return exe_path.substr(0, last_slash);
    }
    return "."; // fallback to current directory
}
#else
std::string GetExecutableDirectory() {
    char path[1024];
    ssize_t len = readlink("/proc/self/exe", path, sizeof(path) - 1);
    if (len != -1) {
        path[len] = '\0';
        std::string exe_path(path);
        size_t last_slash = exe_path.find_last_of('/');
        if (last_slash != std::string::npos) {
            return exe_path.substr(0, last_slash);
        }
    }
    return "."; // fallback to current directory
}
#endif

AudioEngine::AudioEngine()
    : initialized_(false)
    , midi_stream_(0)
    , soundfont_(0)
    , master_volume_(0.8f)
    , current_soundfont_path_("")
    , max_polyphony_(256)  // Reasonable polyphony for most use cases
    , current_polyphony_(0)
    , midi_input_(0)
    , current_midi_input_device_(-1)
    , current_midi_input_name_("")
    , alsa_midi_(std::make_unique<ALSAMIDIInput>())
    , winmm_midi_(std::make_unique<WinMMMIDIInput>())
    , ext_process_midi_(std::make_unique<ExtProcessMIDIInput>())
    , piano_keyboard_(nullptr)
    , audio_limiter_()
    , limiter_dsp_(0)
    , bassfx_enabled_(false)
    , reverb_fx_(0)
    , chorus_fx_(0)
    , echo_fx_(0)
    , compressor_fx_(0)
    , reverb_mix_(0.3f)
    , chorus_mix_(0.2f)
    , echo_mix_(0.15f)
    , compressor_ratio_(4.0f)


    , bass_lib_(nullptr)
    , bassmidi_lib_(nullptr)
    , BASS_Init_ptr(nullptr)
    , BASS_Free_ptr(nullptr)
    , BASS_MIDI_StreamCreate_ptr(nullptr)
    , BASS_ChannelPlay_ptr(nullptr)
    , BASS_ChannelSetAttribute_ptr(nullptr)
    , BASS_StreamFree_ptr(nullptr)
    , BASS_MIDI_StreamEvent_ptr(nullptr)
    , BASS_MIDI_FontInit_ptr(nullptr)
    , BASS_MIDI_FontFree_ptr(nullptr)
    , BASS_MIDI_StreamSetFonts_ptr(nullptr)
    , BASS_ChannelSetFX_ptr(nullptr)
    , BASS_ChannelRemoveFX_ptr(nullptr)
    , BASS_FXSetParameters_ptr(nullptr)
    , BASS_FXGetParameters_ptr(nullptr)
    , BASS_ErrorGetCode_ptr(nullptr)
    , BASS_ChannelGetAttribute_ptr(nullptr)
    , BASS_ChannelIsActive_ptr(nullptr)
    , BASS_GetDeviceInfo_ptr(nullptr)
    , BASS_SetConfig_ptr(nullptr)
    , BASS_GetCPU_ptr(nullptr)
    , BASS_GetInfo_ptr(nullptr)
    , BASS_RecordGetDeviceInfo_ptr(nullptr)
    , BASS_RecordInit_ptr(nullptr)
    , BASS_RecordFree_ptr(nullptr)
    , BASS_RecordStart_ptr(nullptr)
    , BASS_ChannelStop_ptr(nullptr)
{
}

AudioEngine::~AudioEngine() {
    Cleanup();
    UnloadBASSLibraries();
}

bool AudioEngine::Initialize() {
    if (initialized_) {
        return true;
    }

    std::cout << "Initializing Audio Engine..." << std::endl;

    if (!LoadBASSLibraries()) {
        std::cerr << "Failed to load BASS libraries" << std::endl;
        return false;
    }

    if (!InitializeBASS()) {
        std::cerr << "Failed to initialize BASS" << std::endl;
        UnloadBASSLibraries();
        return false;
    }

    if (!InitializeMIDI()) {
        std::cerr << "Failed to initialize BASSMIDI" << std::endl;
        CleanupBASS();
        UnloadBASSLibraries();
        return false;
    }

    // Initialize MIDI input (optional, don't fail if it doesn't work)
    if (!InitializeMIDIInput()) {
        std::cout << "MIDI input initialization failed, but continuing..." << std::endl;
    }

    // Initialize platform-specific MIDI input (optional, don't fail if it doesn't work)
#ifdef _WIN32
    if (!InitializeWinMMMIDI()) {
        std::cout << "Windows MIDI input initialization failed, but continuing..." << std::endl;
    }
#else
    if (!InitializeALSAMIDI()) {
        std::cout << "ALSA MIDI input initialization failed, but continuing..." << std::endl;
    }
#endif

    // Initialize External Process MIDI input (optional)
    if (!InitializeExtProcessMIDI()) {
        std::cout << "External Process MIDI input initialization failed, but continuing..." << std::endl;
    } else {
        std::cout << "External Process MIDI input initialized successfully" << std::endl;
    }



    initialized_ = true;
    std::cout << "Audio Engine initialized successfully" << std::endl;
    return true;
}

void AudioEngine::Cleanup() {
    if (!initialized_) {
        return;
    }

    std::cout << "Cleaning up Audio Engine..." << std::endl;

    StopAllNotes();
    CleanupMIDIInput();
#ifdef _WIN32
    CleanupWinMMMIDI();
#else
    CleanupALSAMIDI();
#endif
    CleanupExtProcessMIDI();
    CleanupMIDI();
    CleanupBASS();

    initialized_ = false;
    std::cout << "Audio Engine cleaned up" << std::endl;
}

bool AudioEngine::LoadSoundfont(const std::string& soundfont_path) {
    if (!initialized_ || !BASS_MIDI_FontInit_ptr || !BASS_MIDI_StreamSetFonts_ptr) {
        std::cerr << "Audio engine not initialized or BASS functions not available" << std::endl;
        return false;
    }

    // Free existing soundfont if any
    if (soundfont_ != 0 && BASS_MIDI_FontFree_ptr) {
        BASS_MIDI_FontFree_ptr(soundfont_);
        soundfont_ = 0;
        current_soundfont_path_.clear();
    }

    // Try different paths for soundfont
    std::string exe_dir = GetExecutableDirectory();
    std::vector<std::string> soundfont_paths;
    soundfont_paths.push_back(soundfont_path);                           // User specified path
    soundfont_paths.push_back(JoinPath(".", soundfont_path));             // Current directory
    soundfont_paths.push_back(JoinPath(exe_dir, soundfont_path));        // Executable directory
    soundfont_paths.push_back(JoinPath(JoinPath(".", "soundfonts"), soundfont_path)); // Soundfonts subdirectory
    soundfont_paths.push_back(JoinPath(JoinPath(exe_dir, "soundfonts"), soundfont_path)); // Executable/soundfonts directory
#ifndef _WIN32
    soundfont_paths.push_back(JoinPath("/usr/share/soundfonts", soundfont_path)); // System soundfonts directory
    soundfont_paths.push_back(JoinPath("/usr/local/share/soundfonts", soundfont_path)); // Local soundfonts directory
#endif

    HSOUNDFONT loaded_soundfont = 0;
    std::string successful_path;

    for (const auto& path : soundfont_paths) {
        loaded_soundfont = BASS_MIDI_FontInit_ptr(path.c_str(), BASS_MIDI_FONT_MMAP);
        if (loaded_soundfont != 0) {
            successful_path = path;
            std::cout << "BASS_MIDI_FontInit succeeded for: " << path << std::endl;
            break;
        } else {
            std::cout << "BASS_MIDI_FontInit failed for: " << path << " - " << GetLastBASSError() << std::endl;
        }
    }

    if (loaded_soundfont == 0) {
        std::cerr << "Failed to load soundfont from any location. Tried paths:" << std::endl;
        for (const auto& path : soundfont_paths) {
            std::cerr << "  " << path << std::endl;
        }
        std::cerr << "Note: You can download a soundfont file (.sf2 or .sfz) and place it in the executable directory" << std::endl;
        return false;
    }

    soundfont_ = loaded_soundfont;

    // Set the soundfont for the MIDI stream
    BASS_MIDI_FONT font;
    font.font = soundfont_;
    font.preset = -1;  // Use all presets
    font.bank = 0;     // Use bank 0

    if (!BASS_MIDI_StreamSetFonts_ptr(midi_stream_, &font, 1)) {
        std::cerr << "Failed to set soundfont for MIDI stream - " << GetLastBASSError() << std::endl;
        LogBASSError("BASS_MIDI_StreamSetFonts");
        return false;
    } else {
        std::cout << "BASS_MIDI_StreamSetFonts succeeded" << std::endl;
    }

    current_soundfont_path_ = successful_path;
    std::cout << "Soundfont loaded successfully from: " << successful_path << std::endl;
    return true;
}

void AudioEngine::PlayNote(int note, int velocity, bool from_midi_input) {
    std::cout << "AudioEngine::PlayNote (3-param) called - Note: " << note << ", Velocity: " << velocity
              << ", From MIDI: " << (from_midi_input ? "true" : "false") << " -> calling 4-param version" << std::endl;
    // Call the overloaded version with channel 0 (default channel)
    PlayNote(note, velocity, from_midi_input, 0);
}

void AudioEngine::PlayNote(int note, int velocity, bool from_midi_input, int channel) {
    static int call_counter = 0;
    static std::map<int, int> note_call_count;
    call_counter++;
    note_call_count[note]++;

    std::cout << "AudioEngine::PlayNote called [" << call_counter << "] - Note: " << note
              << " (count for this note: " << note_call_count[note] << ")"
              << ", Velocity: " << velocity
              << ", From MIDI: " << (from_midi_input ? "true" : "false")
              << ", Channel: " << channel << std::endl;

    if (!initialized_) {
        std::cout << "Playing note: " << note << " (velocity: " << velocity << ") - Audio not available" << std::endl;
        return;
    }





    // Fallback to single-threaded MIDI
    if (midi_stream_ == 0 || !BASS_MIDI_StreamEvent_ptr) {
        std::cout << "Playing note: " << note << " (velocity: " << velocity << ") - MIDI not available" << std::endl;
        return;
    }

    static bool channel_setup = false;
    if (!channel_setup) {
        std::cout << "Setting up MIDI channel 0..." << std::endl;
        if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_PROGRAM, 0)) {
            LogBASSError("BASS_MIDI_StreamEvent (Program Change)");
        }
        if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_VOLUME, 127)) {
            LogBASSError("BASS_MIDI_StreamEvent (Volume)");
        }
        if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_EXPRESSION, 127)) {
            LogBASSError("BASS_MIDI_StreamEvent (Expression)");
        }
        channel_setup = true;
    }

    unsigned int midi_param = MAKEWORD(note, velocity);
    std::cout << "Calling BASS_MIDI_StreamEvent - Note: " << note << ", Velocity: " << velocity
              << ", Channel: " << channel << ", MIDI param: " << midi_param << std::endl;
    if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTE, midi_param)) {
        LogBASSError("BASS_MIDI_StreamEvent (PlayNote)");
    } else {
        std::cout << "BASS_MIDI_StreamEvent succeeded" << std::endl;
    }

    // Notify piano keyboard for visual feedback only if from MIDI input
    if (from_midi_input) {
        NotifyMIDIKeyPressed(note, true, channel);
    }
}

void AudioEngine::StopNote(int note, bool from_midi_input) {
    // Call the overloaded version with channel 0 (default channel)
    StopNote(note, from_midi_input, 0);
}

void AudioEngine::StopNote(int note, bool from_midi_input, int channel) {
    static int stop_call_counter = 0;
    static std::map<int, int> note_stop_count;
    stop_call_counter++;
    note_stop_count[note]++;

    std::cout << "AudioEngine::StopNote called [" << stop_call_counter << "] - Note: " << note
              << " (stop count for this note: " << note_stop_count[note] << ")"
              << ", From MIDI: " << (from_midi_input ? "true" : "false")
              << ", Channel: " << channel << std::endl;

    if (!initialized_) {
        return;
    }





    // Fallback to single-threaded MIDI
    if (midi_stream_ == 0 || !BASS_MIDI_StreamEvent_ptr) {
        return;
    }

    unsigned int midi_param = MAKEWORD(note, 0);
    std::cout << "Calling BASS_MIDI_StreamEvent (StopNote) - Note: " << note
              << ", Channel: " << channel << ", MIDI param: " << midi_param << std::endl;
    if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTE, midi_param)) {
        LogBASSError("BASS_MIDI_StreamEvent (StopNote)");
    } else {
        std::cout << "BASS_MIDI_StreamEvent (StopNote) succeeded" << std::endl;
    }

    // Notify piano keyboard for visual feedback only if from MIDI input
    if (from_midi_input) {
        NotifyMIDIKeyPressed(note, false, channel);
    }
}

void AudioEngine::StopAllNotes() {
    if (!initialized_) {
        return;
    }





    // Fallback to single-threaded MIDI
    if (midi_stream_ != 0 && BASS_MIDI_StreamEvent_ptr) {
        for (int channel = 0; channel < 16; ++channel) {
            BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTESOFF, 0);
        }
    }
}

void AudioEngine::ProcessAudio() {
    if (!initialized_) {
        return;
    }

    // Single-threaded MIDI is processed automatically by BASS
    // No additional processing needed
}

void AudioEngine::SetVolume(float volume) {
    master_volume_ = std::max(0.0f, std::min(1.0f, volume));
    if (initialized_ && midi_stream_ != 0 && BASS_ChannelSetAttribute_ptr) {
        BASS_ChannelSetAttribute_ptr(midi_stream_, BASS_ATTRIB_VOL, master_volume_);
    }
}

float AudioEngine::GetVolume() const {
    return master_volume_;
}

bool AudioEngine::IsInitialized() const {
    return initialized_;
}

bool AudioEngine::IsSoundfontLoaded() const {
    return soundfont_ != 0;
}

const std::string& AudioEngine::GetCurrentSoundfontPath() const {
    return current_soundfont_path_;
}

#ifdef _WIN32
bool AudioEngine::LoadBASSLibraries() {
    std::string exe_dir = GetExecutableDirectory();

    std::vector<std::string> bass_paths_vec = { "bass.dll", JoinPath(exe_dir, "bass.dll") };
    for (const auto& path : bass_paths_vec) {
        bass_lib_ = LoadLibraryA(path.c_str());
        if (bass_lib_) {
            std::cout << "Loaded BASS library from: " << path << std::endl;
            break;
        }
    }
    if (!bass_lib_) {
        std::cerr << "Failed to load bass.dll" << std::endl;
        return false;
    }

    std::vector<std::string> bassmidi_paths_vec = { "bassmidi.dll", JoinPath(exe_dir, "bassmidi.dll") };
    for (const auto& path : bassmidi_paths_vec) {
        bassmidi_lib_ = LoadLibraryA(path.c_str());
        if (bassmidi_lib_) {
            std::cout << "Loaded BASSMIDI library from: " << path << std::endl;
            break;
        }
    }
    if (!bassmidi_lib_) {
        std::cerr << "Failed to load bassmidi.dll" << std::endl;
        FreeLibrary((HMODULE)bass_lib_);
        bass_lib_ = nullptr;
        return false;
    }



    #define LOAD_BASS_FUNC(lib, func) func##_ptr = (decltype(func##_ptr))GetProcAddress((HMODULE)lib, #func)
    
    LOAD_BASS_FUNC(bass_lib_, BASS_Init);
    LOAD_BASS_FUNC(bass_lib_, BASS_Free);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelPlay);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelGetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelIsActive);
    LOAD_BASS_FUNC(bass_lib_, BASS_StreamFree);
    LOAD_BASS_FUNC(bass_lib_, BASS_ErrorGetCode);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetDeviceInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_SetConfig);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetCPU);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordGetDeviceInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordInit);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordFree);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordStart);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelStop);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetDSP);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelRemoveDSP);

    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamCreate);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamEvent);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontInit);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontFree);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamSetFonts);

    // Load BASS FX functions
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetFX);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelRemoveFX);
    LOAD_BASS_FUNC(bass_lib_, BASS_FXSetParameters);
    LOAD_BASS_FUNC(bass_lib_, BASS_FXGetParameters);

    #undef LOAD_BASS_FUNC

    if (!BASS_Init_ptr || !BASS_Free_ptr || !BASS_MIDI_StreamCreate_ptr ||
        !BASS_ChannelPlay_ptr || !BASS_MIDI_StreamEvent_ptr) {
        std::cerr << "Failed to load required BASS functions" << std::endl;
        UnloadBASSLibraries();
        return false;
    }

    std::cout << "BASS libraries loaded successfully" << std::endl;
    return true;
}

void AudioEngine::UnloadBASSLibraries() {
    if (bassmidi_lib_) FreeLibrary((HMODULE)bassmidi_lib_);
    if (bass_lib_) FreeLibrary((HMODULE)bass_lib_);

    bass_lib_ = nullptr;
    bassmidi_lib_ = nullptr;

    BASS_Init_ptr = nullptr;
    BASS_Free_ptr = nullptr;
    BASS_MIDI_StreamCreate_ptr = nullptr;
    BASS_ChannelPlay_ptr = nullptr;
    BASS_ChannelSetAttribute_ptr = nullptr;
    BASS_StreamFree_ptr = nullptr;
    BASS_MIDI_StreamEvent_ptr = nullptr;
    BASS_MIDI_FontInit_ptr = nullptr;
    BASS_MIDI_FontFree_ptr = nullptr;
    BASS_MIDI_StreamSetFonts_ptr = nullptr;
    BASS_ErrorGetCode_ptr = nullptr;
    BASS_ChannelGetAttribute_ptr = nullptr;
    BASS_ChannelIsActive_ptr = nullptr;
    BASS_GetDeviceInfo_ptr = nullptr;
    BASS_SetConfig_ptr = nullptr;
}

#else // NOT _WIN32

bool AudioEngine::LoadBASSLibraries() {
    std::string exe_dir = GetExecutableDirectory();

    const char* bass_paths[] = { "libbass.so", "./libbass.so", JoinPath(exe_dir, "libbass.so").c_str() };
    for (const char* path : bass_paths) {
        bass_lib_ = dlopen(path, RTLD_LAZY);
        if (bass_lib_) {
            std::cout << "Loaded BASS library from: " << path << std::endl;
            break;
        }
    }
    if (!bass_lib_) {
        std::cerr << "Failed to load libbass.so. Error: " << dlerror() << std::endl;
        return false;
    }

    const char* bassmidi_paths[] = { "libbassmidi.so", "./libbassmidi.so", JoinPath(exe_dir, "libbassmidi.so").c_str() };
    for (const char* path : bassmidi_paths) {
        bassmidi_lib_ = dlopen(path, RTLD_LAZY);
        if (bassmidi_lib_) {
            std::cout << "Loaded BASSMIDI library from: " << path << std::endl;
            break;
        }
    }
    if (!bassmidi_lib_) {
        std::cerr << "Failed to load libbassmidi.so. Error: " << dlerror() << std::endl;
        dlclose(bass_lib_);
        return false;
    }
    


    #define LOAD_BASS_FUNC(lib, func) func##_ptr = (decltype(func##_ptr))dlsym(lib, #func)

    LOAD_BASS_FUNC(bass_lib_, BASS_Init);
    LOAD_BASS_FUNC(bass_lib_, BASS_Free);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelPlay);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelGetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelIsActive);
    LOAD_BASS_FUNC(bass_lib_, BASS_StreamFree);
    LOAD_BASS_FUNC(bass_lib_, BASS_ErrorGetCode);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetDeviceInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_SetConfig);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetCPU);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordGetDeviceInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordInit);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordFree);
    LOAD_BASS_FUNC(bass_lib_, BASS_RecordStart);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelStop);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetDSP);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelRemoveDSP);

    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamCreate);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamEvent);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontInit);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontFree);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamSetFonts);

    // Load BASS FX functions
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetFX);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelRemoveFX);
    LOAD_BASS_FUNC(bass_lib_, BASS_FXSetParameters);
    LOAD_BASS_FUNC(bass_lib_, BASS_FXGetParameters);

    #undef LOAD_BASS_FUNC

    if (!BASS_Init_ptr || !BASS_Free_ptr || !BASS_MIDI_StreamCreate_ptr ||
        !BASS_ChannelPlay_ptr || !BASS_MIDI_StreamEvent_ptr) {
        std::cerr << "Failed to load required BASS functions" << std::endl;
        UnloadBASSLibraries();
        return false;
    }
    return true;
}

void AudioEngine::UnloadBASSLibraries() {
    if (bassmidi_lib_) dlclose(bassmidi_lib_);
    if (bass_lib_) dlclose(bass_lib_);

    bass_lib_ = nullptr;
    bassmidi_lib_ = nullptr;

    BASS_Init_ptr = nullptr;
    BASS_Free_ptr = nullptr;
    BASS_MIDI_StreamCreate_ptr = nullptr;
    BASS_ChannelPlay_ptr = nullptr;
    BASS_ChannelSetAttribute_ptr = nullptr;
    BASS_StreamFree_ptr = nullptr;
    BASS_MIDI_StreamEvent_ptr = nullptr;
    BASS_MIDI_FontInit_ptr = nullptr;
    BASS_MIDI_FontFree_ptr = nullptr;
    BASS_MIDI_StreamSetFonts_ptr = nullptr;

    // Reset BASS FX function pointers
    BASS_ChannelSetFX_ptr = nullptr;
    BASS_ChannelRemoveFX_ptr = nullptr;
    BASS_FXSetParameters_ptr = nullptr;
    BASS_FXGetParameters_ptr = nullptr;
    BASS_ErrorGetCode_ptr = nullptr;
    BASS_ChannelGetAttribute_ptr = nullptr;
    BASS_ChannelIsActive_ptr = nullptr;
    BASS_GetDeviceInfo_ptr = nullptr;
    BASS_SetConfig_ptr = nullptr;
    BASS_GetCPU_ptr = nullptr;
    BASS_GetInfo_ptr = nullptr;
    BASS_RecordGetDeviceInfo_ptr = nullptr;
    BASS_RecordInit_ptr = nullptr;
    BASS_RecordFree_ptr = nullptr;
    BASS_RecordStart_ptr = nullptr;
    BASS_ChannelStop_ptr = nullptr;
    BASS_ChannelSetDSP_ptr = nullptr;
    BASS_ChannelRemoveDSP_ptr = nullptr;
    BASS_ChannelSetDSP_ptr = nullptr;
    BASS_ChannelRemoveDSP_ptr = nullptr;
}
#endif

// BASS DSP callback function for Audio Limiter
void CALLBACK AudioLimiterDSPCallback(HDSP handle, DWORD channel, void* buffer, DWORD length, void* user) {
    AudioEngine* audio_engine = static_cast<AudioEngine*>(user);
    if (!audio_engine) return;

    AudioLimiter* limiter = audio_engine->GetAudioLimiter();
    if (!limiter || !limiter->IsEnabled()) return;

    // Process audio samples (assuming 32-bit float format)
    float* samples = static_cast<float*>(buffer);
    int sample_count = length / (sizeof(float) * 2); // stereo

    limiter->ProcessSamples(samples, sample_count);
}

// BASS MIDI input callback function
BOOL CALLBACK BASSMIDIInputCallback(HRECORD handle, const void* buffer, DWORD length, void* user) {
    AudioEngine* audio_engine = static_cast<AudioEngine*>(user);
    if (!audio_engine) return TRUE;

    const BYTE* midi_data = static_cast<const BYTE*>(buffer);

    // Process MIDI messages
    for (DWORD i = 0; i < length; i += 3) {
        if (i + 2 < length) {
            BYTE status = midi_data[i];
            BYTE data1 = midi_data[i + 1];
            BYTE data2 = midi_data[i + 2];

            // Extract channel information from status byte
            int channel = status & 0x0F;

            // Note On message (0x90-0x9F)
            if ((status & 0xF0) == 0x90 && data2 > 0) {
                audio_engine->PlayNote(data1, data2, true, channel);
            }
            // Note Off message (0x80-0x8F) or Note On with velocity 0
            else if ((status & 0xF0) == 0x80 || ((status & 0xF0) == 0x90 && data2 == 0)) {
                audio_engine->StopNote(data1, true, channel);
            }
        }
    }

    return TRUE; // Continue recording
}

std::string GetBASSErrorString(int errorCode) {
    switch (errorCode) {
        case 0: return "BASS_OK - All is OK";
        case 1: return "BASS_ERROR_MEM - Memory error";
        case 2: return "BASS_ERROR_FILEOPEN - Can't open the file";
        case 3: return "BASS_ERROR_DRIVER - Can't find a free/valid driver";
        case 4: return "BASS_ERROR_BUFLOST - The sample buffer was lost";
        case 5: return "BASS_ERROR_HANDLE - Invalid handle";
        case 6: return "BASS_ERROR_FORMAT - Unsupported sample format";
        case 7: return "BASS_ERROR_POSITION - Invalid position";
        case 8: return "BASS_ERROR_INIT - BASS_Init has not been successfully called";
        case 9: return "BASS_ERROR_START - BASS_Start has not been successfully called";
        case 14: return "BASS_ERROR_ALREADY - Already initialized/paused/whatever";
        case 18: return "BASS_ERROR_NOCHAN - Can't get a free channel";
        case 19: return "BASS_ERROR_ILLTYPE - An illegal type was specified";
        case 20: return "BASS_ERROR_ILLPARAM - An illegal parameter was specified";
        case 21: return "BASS_ERROR_NO3D - No 3D support";
        case 22: return "BASS_ERROR_NOEAX - No EAX support";
        case 23: return "BASS_ERROR_DEVICE - Illegal device number";
        case 24: return "BASS_ERROR_NOPLAY - Not playing";
        case 25: return "BASS_ERROR_FREQ - Illegal sample rate";
        case 27: return "BASS_ERROR_NOTFILE - The stream is not a file stream";
        case 29: return "BASS_ERROR_NOHW - No hardware voices available";
        case 31: return "BASS_ERROR_EMPTY - The MOD music has no sequence data";
        case 32: return "BASS_ERROR_NONET - No internet connection could be opened";
        case 33: return "BASS_ERROR_CREATE - Couldn't create the file";
        case 34: return "BASS_ERROR_NOFX - Effects are not available";
        case 37: return "BASS_ERROR_NOTAVAIL - Requested data is not available";
        case 38: return "BASS_ERROR_DECODE - The channel is/was decoding";
        case 39: return "BASS_ERROR_DX - A sufficient DirectX version is not installed";
        case 40: return "BASS_ERROR_TIMEOUT - Connection timed out";
        case 41: return "BASS_ERROR_FILEFORM - Unsupported file format";
        case 42: return "BASS_ERROR_SPEAKER - Unavailable speaker";
        case 43: return "BASS_ERROR_VERSION - Invalid BASS version";
        case 44: return "BASS_ERROR_CODEC - Codec is not available/supported";
        case 45: return "BASS_ERROR_ENDED - The channel/file has ended";
        case 46: return "BASS_ERROR_BUSY - The device is busy";
        default: return "Unknown error code: " + std::to_string(errorCode);
    }
}

bool AudioEngine::InitializeBASS() {
    if (!BASS_Init_ptr) {
        std::cerr << "BASS_Init function not available" << std::endl;
        return false;
    }

    // Set config for lower latency before init (conservative settings)
    if (BASS_SetConfig_ptr) {
        // Reduce update period for lower latency (7ms - balanced)
        BASS_SetConfig_ptr(BASS_CONFIG_UPDATEPERIOD, 7);

        // Reduce buffer size for lower latency (15ms - balanced)
        BASS_SetConfig_ptr(BASS_CONFIG_BUFFER, 15);

        // Set MIDI-specific optimizations (conservative)
        BASS_SetConfig_ptr(BASS_CONFIG_MIDI_VOICES, 512); // Keep reasonable voice limit

        // Enable float processing for better performance
        BASS_SetConfig_ptr(BASS_CONFIG_FLOATDSP, 1);

        // Try to force ALSA usage on Linux
        #ifndef _WIN32
        BASS_SetConfig_ptr(BASS_CONFIG_DEV_DEFAULT, 1); // Try to use first available device as default
        #endif
    }

    // Enumerate available devices before attempting initialization
    std::cout << "Enumerating audio devices before initialization..." << std::endl;
    PrintDeviceInfo();

    // Try different initialization strategies for Linux audio systems
    bool initialized = false;

    // Strategy 1: Try device 1 first (often the first real ALSA device)
    std::cout << "Attempting to initialize BASS with device 1 at 48kHz..." << std::endl;
    if (BASS_Init_ptr(1, 48000, 0, nullptr, nullptr)) {
        std::cout << "Successfully initialized with device 1 at 48kHz" << std::endl;
        initialized = true;
    } else {
        LogBASSError("BASS_Init with device 1");

        // Strategy 2: Try default device
        std::cout << "Attempting to initialize BASS with BASS_DEVICE_DEFAULT at 48kHz..." << std::endl;
        if (BASS_Init_ptr(BASS_DEVICE_DEFAULT, 48000, 0, nullptr, nullptr)) {
            std::cout << "Successfully initialized with BASS_DEVICE_DEFAULT at 48kHz" << std::endl;
            initialized = true;
        } else {
            LogBASSError("BASS_Init with BASS_DEVICE_DEFAULT");

            // Strategy 3: Try device -1
            std::cout << "Trying device -1 at 48kHz..." << std::endl;
            if (BASS_Init_ptr(-1, 48000, 0, nullptr, nullptr)) {
                std::cout << "Successfully initialized with device -1 at 48kHz" << std::endl;
                initialized = true;
            } else {
                LogBASSError("BASS_Init with -1");

                // Strategy 4: Try multiple device numbers
                for (int device = 2; device <= 5; device++) {
                    std::cout << "Trying device " << device << " at 48kHz..." << std::endl;
                    if (BASS_Init_ptr(device, 48000, 0, nullptr, nullptr)) {
                        std::cout << "Successfully initialized with device " << device << " at 48kHz" << std::endl;
                        initialized = true;
                        break;
                    } else {
                        LogBASSError("BASS_Init with device " + std::to_string(device));
                    }
                }

                // Final fallback: use device 0 (no sound) to allow the app to run
                if (!initialized) {
                    std::cout << "All real devices failed. Falling back to device 0 (no sound) at 48kHz..." << std::endl;
                    if (BASS_Init_ptr(0, 48000, 0, nullptr, nullptr)) {
                        std::cout << "BASS initialized with no sound device at 48kHz. Audio will be silent." << std::endl;
                        initialized = true;
                    } else {
                        LogBASSError("BASS_Init with device 0 (no sound)");
                        return false;
                    }
                }
            }
        }
    }
    std::cout << "BASS initialized successfully" << std::endl;
    PrintDeviceInfo();
    return true;
}

bool AudioEngine::InitializeMIDI() {
    if (!BASS_MIDI_StreamCreate_ptr || !BASS_ChannelPlay_ptr) {
        std::cerr << "BASSMIDI functions not available" << std::endl;
        return false;
    }
    // Create MIDI stream with optimized flags for low latency
    DWORD flags = BASS_SAMPLE_FLOAT;
    midi_stream_ = BASS_MIDI_StreamCreate_ptr(16, flags, 0);
    if (midi_stream_ == 0) {
        LogBASSError("BASS_MIDI_StreamCreate");
        return false;
    }

    // Set additional stream attributes for low latency (conservative settings)
    if (BASS_ChannelSetAttribute_ptr) {
        // Set buffer length to a reasonable value for lower latency
        BASS_ChannelSetAttribute_ptr(midi_stream_, BASS_ATTRIB_BUFFER, 0.05f); // 50ms buffer (safer)
    }

    // Set the maximum polyphony
    if (BASS_ChannelSetAttribute_ptr) {
        if (BASS_ChannelSetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_VOICES, static_cast<float>(max_polyphony_))) {
            std::cout << "MIDI polyphony set to: " << max_polyphony_ << std::endl;
        } else {
            LogBASSError("BASS_ChannelSetAttribute BASS_ATTRIB_MIDI_VOICES");
        }
    }

    BASS_ChannelPlay_ptr(midi_stream_, FALSE);
    SetVolume(master_volume_);

    // Initialize Audio Limiter
    audio_limiter_.Initialize(48000.0f, 2); // 48kHz, stereo

    // Set up DSP for Audio Limiter
    if (BASS_ChannelSetDSP_ptr) {
        limiter_dsp_ = BASS_ChannelSetDSP_ptr(midi_stream_, AudioLimiterDSPCallback, this, 0);
        if (limiter_dsp_ == 0) {
            LogBASSError("BASS_ChannelSetDSP for Audio Limiter");
        } else {
            std::cout << "Audio Limiter DSP set up successfully" << std::endl;
        }
    }

    return true;
}

void AudioEngine::CleanupBASS() {
    if (BASS_Free_ptr) {
        BASS_Free_ptr();
    }
}

void AudioEngine::CleanupMIDI() {
    // Remove DSP effect
    if (limiter_dsp_ != 0 && BASS_ChannelRemoveDSP_ptr) {
        BASS_ChannelRemoveDSP_ptr(midi_stream_, limiter_dsp_);
        limiter_dsp_ = 0;
    }

    if (soundfont_ != 0 && BASS_MIDI_FontFree_ptr) {
        BASS_MIDI_FontFree_ptr(soundfont_);
        soundfont_ = 0;
    }
    if (midi_stream_ != 0 && BASS_StreamFree_ptr) {
        BASS_StreamFree_ptr(midi_stream_);
        midi_stream_ = 0;
    }
}

std::string AudioEngine::GetLastBASSError() const {
    if (!BASS_ErrorGetCode_ptr) {
        return "BASS_ErrorGetCode function not available";
    }
    return GetBASSErrorString(BASS_ErrorGetCode_ptr());
}

void AudioEngine::LogBASSError(const std::string& operation) const {
    std::cerr << "BASS Error in " << operation << ": " << GetLastBASSError() << std::endl;
}

int AudioEngine::GetCurrentPolyphony() const {
    if (!initialized_ || midi_stream_ == 0 || !BASS_ChannelGetAttribute_ptr) {
        return 0;
    }
    float voices = 0.0f;
    if (BASS_ChannelGetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_VOICES_ACTIVE, &voices)) {
        return static_cast<int>(voices);
    }
    return 0;
}

int AudioEngine::GetMaxPolyphony() const {
    if (!initialized_ || midi_stream_ == 0 || !BASS_ChannelGetAttribute_ptr) {
        return max_polyphony_;
    }
    float max_voices = 0.0f;
    if (BASS_ChannelGetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_VOICES, &max_voices)) {
        return static_cast<int>(max_voices);
    }
    return max_polyphony_;
}

bool AudioEngine::SetMaxPolyphony(int max_polyphony) {
    if (max_polyphony < 1 || max_polyphony > 5000) {
        std::cerr << "Invalid polyphony value: " << max_polyphony << ". Must be between 1 and 5000." << std::endl;
        return false;
    }

    max_polyphony_ = max_polyphony;

    if (initialized_ && midi_stream_ != 0 && BASS_ChannelSetAttribute_ptr) {
        // Set the maximum number of voices for the MIDI stream
        if (BASS_ChannelSetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_VOICES, static_cast<float>(max_polyphony))) {
            std::cout << "Maximum polyphony set to: " << max_polyphony << std::endl;
            return true;
        } else {
            LogBASSError("BASS_ChannelSetAttribute BASS_ATTRIB_MIDI_VOICES");
            return false;
        }
    }

    std::cout << "Maximum polyphony will be set to " << max_polyphony << " when audio is initialized." << std::endl;
    return true;
}

void AudioEngine::PlayTestTone() {
    if (!initialized_) return;
    PlayNote(60, 100, false); // Middle C - not from MIDI input
}

bool AudioEngine::PanicRestart() {
    std::cout << "PANIC: Attempting emergency restart of audio engine..." << std::endl;

    // Store current configuration
    std::string current_soundfont = current_soundfont_path_;
    float current_volume = master_volume_;
    int current_polyphony = max_polyphony_;

    // Emergency stop all notes first
    EmergencyStopAllNotes();

    // Full cleanup and restart
    Cleanup();

    // Wait a moment for cleanup to complete
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // Reinitialize
    if (!Initialize()) {
        std::cerr << "PANIC: Failed to reinitialize audio engine!" << std::endl;
        return false;
    }

    // Restore configuration
    SetVolume(current_volume);
    SetMaxPolyphony(current_polyphony);

    // Reload soundfont if one was loaded
    if (!current_soundfont.empty()) {
        if (!LoadSoundfont(current_soundfont)) {
            std::cout << "PANIC: Warning - Failed to reload soundfont: " << current_soundfont << std::endl;
        }
    }

    std::cout << "PANIC: Audio engine restart completed successfully!" << std::endl;
    return true;
}

bool AudioEngine::IsAudioWorking() const {
    if (!initialized_) {
        return false;
    }

    // Check if MIDI stream is still valid
    if (midi_stream_ == 0) {
        return false;
    }

    // Check if BASS is still active
    if (BASS_ChannelIsActive_ptr && BASS_ChannelIsActive_ptr(midi_stream_) == BASS_ACTIVE_STOPPED) {
        return false;
    }

    // Check for BASS errors
    if (BASS_ErrorGetCode_ptr) {
        int error_code = BASS_ErrorGetCode_ptr();
        if (error_code != BASS_OK && error_code != BASS_ERROR_ENDED) {
            return false;
        }
    }

    return true;
}

void AudioEngine::EmergencyStopAllNotes() {
    std::cout << "EMERGENCY: Stopping all notes immediately..." << std::endl;

    if (!initialized_ || midi_stream_ == 0) {
        return;
    }

    // Try multiple methods to stop all notes
    if (BASS_MIDI_StreamEvent_ptr) {
        // Send All Notes Off on all channels
        for (int channel = 0; channel < 16; ++channel) {
            BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTESOFF, 0);
            BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_SOUNDOFF, 0);
            // Also send All Controllers Off
            BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_RESET, 0);
        }
    }

    // Force stop the channel if possible
    if (BASS_ChannelStop_ptr) {
        BASS_ChannelStop_ptr(midi_stream_);
    }

    // Restart the channel
    if (BASS_ChannelPlay_ptr) {
        BASS_ChannelPlay_ptr(midi_stream_, FALSE);
    }

    std::cout << "EMERGENCY: All notes stopped" << std::endl;
}

float AudioEngine::GetCPUUsage() const {
    if (!initialized_ || !BASS_GetCPU_ptr) {
        return 0.0f;
    }
    return BASS_GetCPU_ptr();
}

float AudioEngine::GetRenderingTime() const {
    if (!initialized_ || midi_stream_ == 0 || !BASS_ChannelGetAttribute_ptr) {
        return 0.0f;
    }
    float cpu_time = 0.0f;
    if (BASS_ChannelGetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_CPU, &cpu_time)) {
        return cpu_time;
    }
    return 0.0f;
}

int AudioEngine::GetActiveChannels() const {
    if (!initialized_ || midi_stream_ == 0 || !BASS_ChannelGetAttribute_ptr) {
        return 0;
    }
    float channels = 0.0f;
    if (BASS_ChannelGetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_CHANS, &channels)) {
        return static_cast<int>(channels);
    }
    return 0;
}

std::string AudioEngine::GetAudioInfo() const {
    if (!initialized_ || !BASS_GetInfo_ptr) {
        return "Audio not initialized";
    }

    BASS_INFO info;
    if (BASS_GetInfo_ptr(&info)) {
        std::string result = "Audio Info:\n";
        result += "Min Buffer: " + std::to_string(info.minbuf) + "ms\n";
        result += "Latency: " + std::to_string(info.latency) + "ms\n";
        result += "Frequency: " + std::to_string(info.freq) + "Hz\n";
        result += "Speakers: " + std::to_string(info.speakers) + "\n";
        return result;
    }
    return "Failed to get audio info";
}

bool AudioEngine::InitializeMIDIInput() {
    if (!BASS_RecordInit_ptr || !BASS_RecordGetDeviceInfo_ptr) {
        std::cout << "MIDI input functions not available" << std::endl;
        return false;
    }

    // Initialize recording system (needed for MIDI input)
    if (!BASS_RecordInit_ptr(-1)) {
        LogBASSError("BASS_RecordInit for MIDI input");
        return false;
    }

    std::cout << "MIDI input system initialized" << std::endl;
    return true;
}

void AudioEngine::CleanupMIDIInput() {
    CloseMIDIInputDevice();

    if (BASS_RecordFree_ptr) {
        BASS_RecordFree_ptr();
    }
}

std::vector<std::string> AudioEngine::GetMIDIInputDevices() const {
    std::vector<std::string> devices;

    if (!BASS_RecordGetDeviceInfo_ptr) {
        return devices;
    }

    BASS_DEVICEINFO info;
    for (int i = 0; BASS_RecordGetDeviceInfo_ptr(i, &info); i++) {
        if (info.flags & BASS_DEVICE_ENABLED) {
            devices.push_back(std::string(info.name));
        }
    }

    return devices;
}

bool AudioEngine::OpenMIDIInputDevice(int device_id) {
    if (!BASS_RecordStart_ptr) {
        std::cerr << "BASS_RecordStart function not available" << std::endl;
        return false;
    }

    // Close existing device if open
    CloseMIDIInputDevice();

    // Start MIDI recording on the specified device
    midi_input_ = BASS_RecordStart_ptr(48000, 1, 0, BASSMIDIInputCallback, this);
    if (midi_input_ == 0) {
        LogBASSError("BASS_RecordStart for MIDI input");
        return false;
    }

    current_midi_input_device_ = device_id;

    // Get device name
    BASS_DEVICEINFO info;
    if (BASS_RecordGetDeviceInfo_ptr && BASS_RecordGetDeviceInfo_ptr(device_id, &info)) {
        current_midi_input_name_ = std::string(info.name);
    } else {
        current_midi_input_name_ = "Device " + std::to_string(device_id);
    }

    std::cout << "MIDI input device opened: " << current_midi_input_name_ << std::endl;
    return true;
}

void AudioEngine::CloseMIDIInputDevice() {
    if (midi_input_ != 0 && BASS_ChannelStop_ptr) {
        BASS_ChannelStop_ptr(midi_input_);
        midi_input_ = 0;
        current_midi_input_device_ = -1;
        current_midi_input_name_.clear();
        std::cout << "MIDI input device closed" << std::endl;
    }
}

bool AudioEngine::IsMIDIInputOpen() const {
    return midi_input_ != 0;
}

std::string AudioEngine::GetCurrentMIDIInputDevice() const {
    return current_midi_input_name_;
}

void AudioEngine::PrintDeviceInfo() {
    if (!BASS_GetDeviceInfo_ptr) {
        std::cout << "BASS_GetDeviceInfo not available" << std::endl;
        return;
    }
    std::cout << "=== BASS Audio Devices === " << std::endl;
    BASS_DEVICEINFO info;
    int device_count = 0;

    // Check device 0 (no sound)
    if (BASS_GetDeviceInfo_ptr(0, &info)) {
        std::cout << "Device 0: " << info.name << " (no sound)" << std::endl;
        device_count++;
    }

    // Check real devices starting from 1
    for (int i = 1; BASS_GetDeviceInfo_ptr(i, &info); i++) {
        std::cout << "Device " << i << ": " << info.name;
        if (info.flags & BASS_DEVICE_DEFAULT) std::cout << " (default)";
        if (info.flags & BASS_DEVICE_ENABLED) std::cout << " (enabled)";
        if (info.flags & BASS_DEVICE_INIT) std::cout << " (initialized)";
        std::cout << std::endl;
        device_count++;
    }

    if (device_count == 0) {
        std::cout << "No audio devices found!" << std::endl;
    } else {
        std::cout << "Total devices found: " << device_count << std::endl;
    }
    std::cout << "==========================" << std::endl;
}

// ALSA MIDI callback function
void ALSAMIDICallbackFunction(const MIDIMessage& message) {
    // This will be set up to call the AudioEngine instance
    // For now, we'll use a global reference (not ideal, but functional)
    extern AudioEngine g_audio;

    // Extract channel information from status byte
    int channel = message.status & 0x0F;

    // Note On message (0x90-0x9F)
    if ((message.status & 0xF0) == 0x90 && message.data2 > 0) {
        g_audio.PlayNote(message.data1, message.data2, true, channel);
    }
    // Note Off message (0x80-0x8F) or Note On with velocity 0
    else if ((message.status & 0xF0) == 0x80 || ((message.status & 0xF0) == 0x90 && message.data2 == 0)) {
        g_audio.StopNote(message.data1, true, channel);
    }
}

bool AudioEngine::InitializeALSAMIDI() {
    if (!alsa_midi_) {
        std::cerr << "ALSA MIDI object not available" << std::endl;
        return false;
    }

    if (!alsa_midi_->Initialize()) {
        std::cout << "Failed to initialize ALSA MIDI" << std::endl;
        return false;
    }

    // Set up callback for MIDI messages
    alsa_midi_->SetMIDICallback(ALSAMIDICallbackFunction);

    std::cout << "ALSA MIDI initialized successfully" << std::endl;
    return true;
}

void AudioEngine::CleanupALSAMIDI() {
    if (alsa_midi_) {
        alsa_midi_->Cleanup();
    }
}

std::vector<ALSAMIDIDevice> AudioEngine::GetALSAMIDIDevices() const {
    if (!alsa_midi_) {
        return std::vector<ALSAMIDIDevice>();
    }

    return alsa_midi_->GetInputDevices();
}

bool AudioEngine::OpenALSAMIDIDevice(int client_id, int port_id) {
    if (!alsa_midi_) {
        std::cerr << "ALSA MIDI not available" << std::endl;
        return false;
    }

    if (!alsa_midi_->OpenDevice(client_id, port_id)) {
        std::cerr << "Failed to open ALSA MIDI device " << client_id << ":" << port_id << std::endl;
        return false;
    }

    if (!alsa_midi_->StartInput()) {
        std::cerr << "Failed to start ALSA MIDI input" << std::endl;
        alsa_midi_->CloseDevice();
        return false;
    }

    std::cout << "ALSA MIDI device opened and input started" << std::endl;
    return true;
}

void AudioEngine::CloseALSAMIDIDevice() {
    if (alsa_midi_) {
        alsa_midi_->StopInput();
        alsa_midi_->CloseDevice();
        std::cout << "ALSA MIDI device closed" << std::endl;
    }
}

bool AudioEngine::IsALSAMIDIOpen() const {
    return alsa_midi_ && alsa_midi_->IsDeviceOpen();
}

ALSAMIDIDevice AudioEngine::GetCurrentALSAMIDIDevice() const {
    if (alsa_midi_) {
        return alsa_midi_->GetCurrentDevice();
    }

    return ALSAMIDIDevice{};
}

void AudioEngine::SetPianoKeyboard(PianoKeyboard* piano_keyboard) {
    piano_keyboard_ = piano_keyboard;
}

void AudioEngine::NotifyMIDIKeyPressed(int note, bool pressed) {
    // Call the overloaded version with channel -1 (no specific channel)
    NotifyMIDIKeyPressed(note, pressed, -1);
}

void AudioEngine::NotifyMIDIKeyPressed(int note, bool pressed, int channel) {
    if (piano_keyboard_) {
        piano_keyboard_->SetMIDIKeyPressed(note, pressed, channel);
    }
}

void AudioEngine::SetLimiterEnabled(bool enabled) {
    audio_limiter_.SetEnabled(enabled);
}

bool AudioEngine::IsLimiterEnabled() const {
    return audio_limiter_.IsEnabled();
}

// Windows MIDI callback function
void WinMMMIDICallbackFunction(const MIDIMessage& message) {
    // This will be set up to call the AudioEngine instance
    // For now, we'll use a global reference (not ideal, but functional)
    extern AudioEngine g_audio;

    // Extract channel information from status byte
    int channel = message.status & 0x0F;

    // Note On message (0x90-0x9F)
    if ((message.status & 0xF0) == 0x90 && message.data2 > 0) {
        g_audio.PlayNote(message.data1, message.data2, true, channel);
    }
    // Note Off message (0x80-0x8F) or Note On with velocity 0
    else if ((message.status & 0xF0) == 0x80 || ((message.status & 0xF0) == 0x90 && message.data2 == 0)) {
        g_audio.StopNote(message.data1, true, channel);
    }
}

bool AudioEngine::InitializeWinMMMIDI() {
#ifdef _WIN32
    if (!winmm_midi_) {
        std::cerr << "Windows MIDI object not available" << std::endl;
        return false;
    }

    if (!winmm_midi_->Initialize()) {
        std::cout << "Failed to initialize Windows MIDI" << std::endl;
        return false;
    }

    // Set up callback for MIDI messages
    winmm_midi_->SetMIDICallback(WinMMMIDICallbackFunction);

    std::cout << "Windows MIDI initialized successfully" << std::endl;
    return true;
#else
    std::cout << "Windows MIDI not available on this platform" << std::endl;
    return false;
#endif
}

void AudioEngine::CleanupWinMMMIDI() {
#ifdef _WIN32
    if (winmm_midi_) {
        winmm_midi_->Cleanup();
    }
#endif
}

std::vector<WinMMMIDIDevice> AudioEngine::GetWinMMMIDIDevices() const {
#ifdef _WIN32
    if (!winmm_midi_) {
        return std::vector<WinMMMIDIDevice>();
    }

    return winmm_midi_->GetInputDevices();
#else
    return std::vector<WinMMMIDIDevice>();
#endif
}

bool AudioEngine::OpenWinMMMIDIDevice(int device_id) {
#ifdef _WIN32
    if (!winmm_midi_) {
        std::cerr << "Windows MIDI not available" << std::endl;
        return false;
    }

    if (!winmm_midi_->OpenDevice(device_id)) {
        std::cerr << "Failed to open Windows MIDI device " << device_id << std::endl;
        return false;
    }

    if (!winmm_midi_->StartInput()) {
        std::cerr << "Failed to start Windows MIDI input" << std::endl;
        winmm_midi_->CloseDevice();
        return false;
    }

    std::cout << "Windows MIDI device opened and input started" << std::endl;
    return true;
#else
    return false;
#endif
}

void AudioEngine::CloseWinMMMIDIDevice() {
#ifdef _WIN32
    if (winmm_midi_) {
        winmm_midi_->StopInput();
        winmm_midi_->CloseDevice();
        std::cout << "Windows MIDI device closed" << std::endl;
    }
#endif
}

bool AudioEngine::IsWinMMMIDIOpen() const {
#ifdef _WIN32
    return winmm_midi_ && winmm_midi_->IsDeviceOpen();
#else
    return false;
#endif
}

WinMMMIDIDevice AudioEngine::GetCurrentWinMMMIDIDevice() const {
#ifdef _WIN32
    if (winmm_midi_) {
        return winmm_midi_->GetCurrentDevice();
    }
#endif

    return WinMMMIDIDevice{};
}

// BASS FX implementation
void AudioEngine::SetBASS_FXEnabled(bool enabled) {
    if (!initialized_ || !midi_stream_) {
        return;
    }

    bassfx_enabled_ = enabled;

    if (!enabled) {
        // Remove all effects when disabled
        if (reverb_fx_) {
            if (BASS_ChannelRemoveFX_ptr) {
                BASS_ChannelRemoveFX_ptr(midi_stream_, reverb_fx_);
            }
            reverb_fx_ = 0;
        }
        if (chorus_fx_) {
            if (BASS_ChannelRemoveFX_ptr) {
                BASS_ChannelRemoveFX_ptr(midi_stream_, chorus_fx_);
            }
            chorus_fx_ = 0;
        }
        if (echo_fx_) {
            if (BASS_ChannelRemoveFX_ptr) {
                BASS_ChannelRemoveFX_ptr(midi_stream_, echo_fx_);
            }
            echo_fx_ = 0;
        }
        if (compressor_fx_) {
            if (BASS_ChannelRemoveFX_ptr) {
                BASS_ChannelRemoveFX_ptr(midi_stream_, compressor_fx_);
            }
            compressor_fx_ = 0;
        }
    }

    std::cout << "BASS FX " << (enabled ? "enabled" : "disabled") << std::endl;
}

bool AudioEngine::IsBASS_FXEnabled() const {
    return bassfx_enabled_;
}

void AudioEngine::SetReverbEnabled(bool enabled) {
    if (!initialized_ || !midi_stream_ || !bassfx_enabled_) {
        return;
    }

    if (enabled && !reverb_fx_) {
        if (BASS_ChannelSetFX_ptr) {
            reverb_fx_ = BASS_ChannelSetFX_ptr(midi_stream_, BASS_FX_DX8_REVERB, 0);
            if (reverb_fx_) {
                BASS_DX8_REVERB reverb_params;
                reverb_params.fInGain = 0.0f;
                reverb_params.fReverbMix = reverb_mix_ * 100.0f - 100.0f; // Convert to dB
                reverb_params.fReverbTime = 1000.0f;
                reverb_params.fHighFreqRTRatio = 0.001f;

                if (BASS_FXSetParameters_ptr) {
                    BASS_FXSetParameters_ptr(reverb_fx_, &reverb_params);
                }
                std::cout << "Reverb effect enabled" << std::endl;
            }
        }
    } else if (!enabled && reverb_fx_) {
        if (BASS_ChannelRemoveFX_ptr) {
            BASS_ChannelRemoveFX_ptr(midi_stream_, reverb_fx_);
        }
        reverb_fx_ = 0;
        std::cout << "Reverb effect disabled" << std::endl;
    }
}

void AudioEngine::SetChorusEnabled(bool enabled) {
    if (!initialized_ || !midi_stream_ || !bassfx_enabled_) {
        return;
    }

    if (enabled && !chorus_fx_) {
        if (BASS_ChannelSetFX_ptr) {
            chorus_fx_ = BASS_ChannelSetFX_ptr(midi_stream_, BASS_FX_DX8_CHORUS, 0);
            if (chorus_fx_) {
                BASS_DX8_CHORUS chorus_params;
                chorus_params.fWetDryMix = chorus_mix_ * 100.0f;
                chorus_params.fDepth = 10.0f;
                chorus_params.fFeedback = 25.0f;
                chorus_params.fFrequency = 1.1f;
                chorus_params.lWaveform = 1; // Sine wave
                chorus_params.fDelay = 16.0f;
                chorus_params.lPhase = 3; // 90 degrees

                if (BASS_FXSetParameters_ptr) {
                    BASS_FXSetParameters_ptr(chorus_fx_, &chorus_params);
                }
                std::cout << "Chorus effect enabled" << std::endl;
            }
        }
    } else if (!enabled && chorus_fx_) {
        if (BASS_ChannelRemoveFX_ptr) {
            BASS_ChannelRemoveFX_ptr(midi_stream_, chorus_fx_);
        }
        chorus_fx_ = 0;
        std::cout << "Chorus effect disabled" << std::endl;
    }
}

void AudioEngine::SetEchoEnabled(bool enabled) {
    if (!initialized_ || !midi_stream_ || !bassfx_enabled_) {
        return;
    }

    if (enabled && !echo_fx_) {
        if (BASS_ChannelSetFX_ptr) {
            echo_fx_ = BASS_ChannelSetFX_ptr(midi_stream_, BASS_FX_DX8_ECHO, 0);
            if (echo_fx_) {
                BASS_DX8_ECHO echo_params;
                echo_params.fWetDryMix = echo_mix_ * 100.0f;
                echo_params.fFeedback = 50.0f;
                echo_params.fLeftDelay = 500.0f;
                echo_params.fRightDelay = 500.0f;
                echo_params.lPanDelay = 0;

                if (BASS_FXSetParameters_ptr) {
                    BASS_FXSetParameters_ptr(echo_fx_, &echo_params);
                }
                std::cout << "Echo effect enabled" << std::endl;
            }
        }
    } else if (!enabled && echo_fx_) {
        if (BASS_ChannelRemoveFX_ptr) {
            BASS_ChannelRemoveFX_ptr(midi_stream_, echo_fx_);
        }
        echo_fx_ = 0;
        std::cout << "Echo effect disabled" << std::endl;
    }
}

void AudioEngine::SetCompressorEnabled(bool enabled) {
    if (!initialized_ || !midi_stream_ || !bassfx_enabled_) {
        return;
    }

    if (enabled && !compressor_fx_) {
        if (BASS_ChannelSetFX_ptr) {
            compressor_fx_ = BASS_ChannelSetFX_ptr(midi_stream_, BASS_FX_DX8_COMPRESSOR, 0);
            if (compressor_fx_) {
                BASS_DX8_COMPRESSOR compressor_params;
                compressor_params.fGain = 0.0f;
                compressor_params.fAttack = 10.0f;
                compressor_params.fRelease = 200.0f;
                compressor_params.fThreshold = -20.0f;
                compressor_params.fRatio = compressor_ratio_;
                compressor_params.fPredelay = 4.0f;

                if (BASS_FXSetParameters_ptr) {
                    BASS_FXSetParameters_ptr(compressor_fx_, &compressor_params);
                }
                std::cout << "Compressor effect enabled" << std::endl;
            }
        }
    } else if (!enabled && compressor_fx_) {
        if (BASS_ChannelRemoveFX_ptr) {
            BASS_ChannelRemoveFX_ptr(midi_stream_, compressor_fx_);
        }
        compressor_fx_ = 0;
        std::cout << "Compressor effect disabled" << std::endl;
    }
}

void AudioEngine::SetReverbMix(float mix) {
    reverb_mix_ = std::max(0.0f, std::min(1.0f, mix));

    if (reverb_fx_ && BASS_FXSetParameters_ptr) {
        BASS_DX8_REVERB reverb_params;
        if (BASS_FXGetParameters_ptr && BASS_FXGetParameters_ptr(reverb_fx_, &reverb_params)) {
            reverb_params.fReverbMix = reverb_mix_ * 100.0f - 100.0f; // Convert to dB
            BASS_FXSetParameters_ptr(reverb_fx_, &reverb_params);
        }
    }
}

void AudioEngine::SetChorusMix(float mix) {
    chorus_mix_ = std::max(0.0f, std::min(1.0f, mix));

    if (chorus_fx_ && BASS_FXSetParameters_ptr) {
        BASS_DX8_CHORUS chorus_params;
        if (BASS_FXGetParameters_ptr && BASS_FXGetParameters_ptr(chorus_fx_, &chorus_params)) {
            chorus_params.fWetDryMix = chorus_mix_ * 100.0f;
            BASS_FXSetParameters_ptr(chorus_fx_, &chorus_params);
        }
    }
}

void AudioEngine::SetEchoMix(float mix) {
    echo_mix_ = std::max(0.0f, std::min(1.0f, mix));

    if (echo_fx_ && BASS_FXSetParameters_ptr) {
        BASS_DX8_ECHO echo_params;
        if (BASS_FXGetParameters_ptr && BASS_FXGetParameters_ptr(echo_fx_, &echo_params)) {
            echo_params.fWetDryMix = echo_mix_ * 100.0f;
            BASS_FXSetParameters_ptr(echo_fx_, &echo_params);
        }
    }
}

void AudioEngine::SetCompressorRatio(float ratio) {
    compressor_ratio_ = std::max(1.0f, std::min(20.0f, ratio));

    if (compressor_fx_ && BASS_FXSetParameters_ptr) {
        BASS_DX8_COMPRESSOR compressor_params;
        if (BASS_FXGetParameters_ptr && BASS_FXGetParameters_ptr(compressor_fx_, &compressor_params)) {
            compressor_params.fRatio = compressor_ratio_;
            BASS_FXSetParameters_ptr(compressor_fx_, &compressor_params);
        }
    }
}

bool AudioEngine::IsReverbEnabled() const {
    return reverb_fx_ != 0;
}

bool AudioEngine::IsChorusEnabled() const {
    return chorus_fx_ != 0;
}

bool AudioEngine::IsEchoEnabled() const {
    return echo_fx_ != 0;
}

bool AudioEngine::IsCompressorEnabled() const {
    return compressor_fx_ != 0;
}

// External Process MIDI input functions
bool AudioEngine::InitializeExtProcessMIDI() {
    if (!ext_process_midi_) {
        return false;
    }

    if (!ext_process_midi_->Initialize()) {
        std::cerr << "Failed to initialize External Process MIDI input" << std::endl;
        return false;
    }

    // Set MIDI callback to handle incoming MIDI messages
    ext_process_midi_->SetMIDICallback([this](const MIDIMessage& message) {
        // Convert MIDIMessage to MIDI events and process them
        if ((message.status & 0xF0) == 0x90) { // Note On
            int note = message.data1;
            int velocity = message.data2;
            int channel = message.status & 0x0F;

            std::cout << "External Process MIDI: Note On - Note: " << note << ", Velocity: " << velocity << ", Channel: " << channel << std::endl;

            if (velocity > 0) {
                NotifyMIDIKeyPressed(note, true, channel);
                if (velocity < 10) return;
                std::cout << "External Process MIDI: Calling PlayNote for note " << note << std::endl;
                PlayNote(note, velocity, true, channel);
            } else {
                // Note On with velocity 0 is equivalent to Note Off
                StopNote(note, true, channel);
                NotifyMIDIKeyPressed(note, false, channel);
            }
        } else if ((message.status & 0xF0) == 0x80) { // Note Off
            int note = message.data1;
            int channel = message.status & 0x0F;

            // std::cout << "External Process MIDI: Note Off - Note: " << note << ", Channel: " << channel << std::endl;

            StopNote(note, true, channel);
            NotifyMIDIKeyPressed(note, false, channel);
        }
        // Add more MIDI message types as needed (control changes, etc.)
    });

    std::cout << "External Process MIDI input initialized successfully" << std::endl;
    return true;
}

void AudioEngine::CleanupExtProcessMIDI() {
    if (ext_process_midi_) {
        ext_process_midi_->Cleanup();
    }
}

bool AudioEngine::StartExtProcessMIDI(const std::string& executable_path, const std::vector<std::string>& args) {
    if (!ext_process_midi_) {
        return false;
    }

    return ext_process_midi_->StartInput(executable_path, args);
}

void AudioEngine::StopExtProcessMIDI() {
    if (ext_process_midi_) {
        ext_process_midi_->StopInput();
    }
}

bool AudioEngine::IsExtProcessMIDIActive() const {
    return ext_process_midi_ && ext_process_midi_->IsInputActive();
}

std::string AudioEngine::GetExtProcessMIDIStderrLog() const {
    if (ext_process_midi_) {
        return ext_process_midi_->GetStderrLog();
    }
    return "";
}

void AudioEngine::ClearExtProcessMIDIStderrLog() {
    if (ext_process_midi_) {
        ext_process_midi_->ClearStderrLog();
    }
}